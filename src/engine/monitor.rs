use crate::{
    PREDEFINED_RINGS,
    encoding::order_update::{OrderResponse, parse_order_update},
    utils::{self, perf::circles_to_ns},
};

#[derive(Debug)]
pub struct LatencyStats {
    pub arbitrage_latency_sum: f64,
    pub arbitrage_latency_count: usize,
    pub test_latency_sum: f64,
    pub test_latency_count: usize,
    pub win_count: usize,
    pub loss_count: usize,
}

impl LatencyStats {
    pub fn new() -> Self {
        Self {
            arbitrage_latency_sum: 0.0,
            test_latency_sum: 0.0,
            arbitrage_latency_count: 0,
            test_latency_count: 0,
            win_count: 0,
            loss_count: 0,
        }
    }

    #[inline(always)]
    pub fn add_arbitrage_latency(&mut self, latency_ns: f64) {
        self.arbitrage_latency_sum += latency_ns;
        self.arbitrage_latency_count += 1;
    }

    #[inline(always)]
    pub fn add_test_latency(&mut self, latency_ns: f64) {
        self.test_latency_sum += latency_ns;
        self.test_latency_count += 1;
    }

    pub fn print_stats(&self) {
        if self.arbitrage_latency_count > 0 {
            println!(
                "Arbitrage Order Latency (NEW status): {:.2}us",
                self.arbitrage_latency_sum / self.arbitrage_latency_count as f64 / 1000.0
            );
        }
        if self.test_latency_count > 0 {
            println!(
                "Test Order Latency (NEW status): {:.2}us",
                self.test_latency_sum / self.test_latency_count as f64 / 1000.0
            );
        }
        if self.win_count > 0 {
            println!("Wins: {}", self.win_count);
        }
        if self.loss_count > 0 {
            println!("Losses: {}", self.loss_count);
        }
    }
}

// 全局延迟统计
static mut LATENCY_STATS: Option<LatencyStats> = None;

pub fn init_latency_stats() {
    unsafe {
        LATENCY_STATS = Some(LatencyStats::new());
    }
}

#[inline(always)]
pub fn add_arbitrage_latency(latency_ns: f64) {
    unsafe {
        // 单线程环境，直接访问，无需任何同步开销
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.add_arbitrage_latency(latency_ns);
        }
    }
}

#[inline(always)]
pub fn add_test_latency(latency_ns: f64) {
    unsafe {
        // 单线程环境，直接访问，无需任何同步开销
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.add_test_latency(latency_ns);
        }
    }
}

pub fn print_latency_stats() {
    unsafe {
        if let Some(ref stats) = LATENCY_STATS {
            stats.print_stats();
        }
    }
}

static mut ORDER_STATUS: [u8; 10] = [0; 10]; // 1 for FILLED, 2 for others
pub static mut CURRENT_RING_TS: u64 = 0;
static mut ORDER_FILL_COUNT: usize = 0;

pub fn monitor_order_execution(msg: &[u8]) {
    if let Some(order_res) = parse_order_update(msg) {
        match order_res {
            OrderResponse::OrderUpdate(ou) => {
                let ring_index = ou.order_id.ring_index;
                let ring_len = PREDEFINED_RINGS[ring_index].len();
                let edge_index = ou.order_id.edge_index;
                let order_create_time = ou.order_id.order_create_time;
                let is_testing = ou.order_id.is_testing;
                match ou.status.as_str() {
                    "NEW" => {
                        let now = utils::perf::now();
                        let latency_ns = circles_to_ns(now - order_create_time);
                        if is_testing {
                            add_test_latency(latency_ns);
                        } else {
                            add_arbitrage_latency(latency_ns);
                        }
                    }
                    "REJECTED" | "EXPIRED" | "CANCELED" => {
                        if is_testing {
                            return;
                        }
                        if order_create_time < unsafe { CURRENT_RING_TS } {
                            return;
                        }
                        unsafe {
                            ORDER_STATUS[edge_index] = 2;
                            ORDER_FILL_COUNT += 1;
                            if ORDER_FILL_COUNT == ring_len {
                                ORDER_FILL_COUNT = 0;
                                if let Some(ref mut stats) = LATENCY_STATS {
                                    stats.loss_count += 1;
                                    stats.print_stats();
                                }
                            }
                        }
                    }
                    "TRADE" => {
                        if is_testing {
                            return;
                        }
                        if order_create_time < unsafe { CURRENT_RING_TS } {
                            return;
                        }
                        unsafe {
                            ORDER_STATUS[edge_index] = 1;
                            ORDER_FILL_COUNT += 1;
                            if ORDER_FILL_COUNT == PREDEFINED_RINGS[ring_index].len() {
                                ORDER_FILL_COUNT = 0;
                                let mut sum: usize = 0;
                                for i in 0..ring_len {
                                    sum += ORDER_STATUS[i] as usize;
                                }
                                if sum > ring_len {
                                    if let Some(ref mut stats) = LATENCY_STATS {
                                        stats.loss_count += 1;
                                        stats.print_stats();
                                    }
                                } else {
                                    if let Some(ref mut stats) = LATENCY_STATS {
                                        stats.win_count += 1;
                                        stats.print_stats();
                                    }
                                }
                            }
                        }
                    }
                    _ => {}
                }
            }
            OrderResponse::OrderCreated(_ou) => {}
        }
    }
}
