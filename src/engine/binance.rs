pub fn generate_market_data_ws_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    // SBE格式使用 bestBidAsk 流名称（等同于 JSON 的 bookTicker）
    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@bestBidAsk", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_order_url() -> String {
    "wss://ws-api.binance.com:443/ws-api/v3".to_string()
}
